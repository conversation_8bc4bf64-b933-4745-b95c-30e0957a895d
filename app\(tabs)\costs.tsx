import { CostForm } from '@/components/CostForm';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useSupabaseCosts } from '@/hooks/useSupabaseCosts';
import { useSupabaseProducts } from '@/hooks/useSupabaseProducts';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Cost, CostSummary } from '@/types/Cost';
import { useState } from 'react';
import { Alert, FlatList, StyleSheet, TouchableOpacity } from 'react-native';

export default function CostsScreen() {
  const { costs, loading, error, addCost } = useSupabaseCosts();
  const { products } = useSupabaseProducts();
  const [activeTab, setActiveTab] = useState<'list' | 'form'>('list');

  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');
  const primaryColor = useThemeColor({}, 'primary');
  const borderColor = useThemeColor({}, 'border');

  const handleAddCost = async (
    productName: string,
    quantity: number,
    essencePrice: number,
    essenceAmount: number,
    essenceUsed: number,
    alcoholPrice: number,
    alcoholUsed: number,
    bottlePrice: number
  ) => {
    try {
      await addCost({
        product_name: productName,
        quantity,
        essence_price: essencePrice,
        essence_amount: essenceAmount,
        essence_used: essenceUsed,
        alcohol_price: alcoholPrice,
        alcohol_used: alcoholUsed,
        bottle_price: bottlePrice
      });
      setActiveTab('list');
    } catch (error) {
      console.error('Error adding cost:', error);
      Alert.alert('Hata', 'Maliyet eklenirken bir hata oluştu');
    }
  };

  const calculateSummary = (): CostSummary => {
    if (!costs || costs.length === 0) {
      return {
        totalProducts: 0,
        totalQuantity: 0,
        totalCost: 0,
        averageCost: 0,
        averageUnitCost: 0,
        totalEssenceUsed: 0,
        totalAlcoholUsed: 0
      };
    }

    return costs.reduce((summary, cost) => {
      const quantity = cost.quantity || 0;
      const totalCost = cost.total_cost || cost.totalCost || 0;
      const essenceUsed = cost.essence_used || cost.essenceUsed || 0;
      const alcoholUsed = cost.alcohol_used || cost.alcoholUsed || 0;

      const newTotalProducts = summary.totalProducts + quantity;
      const newTotalQuantity = summary.totalQuantity + quantity;
      const newTotalCost = summary.totalCost + totalCost;

      return {
        totalProducts: newTotalProducts,
        totalQuantity: newTotalQuantity,
        totalCost: newTotalCost,
        averageCost: newTotalProducts > 0 ? newTotalCost / newTotalProducts : 0,
        averageUnitCost: newTotalQuantity > 0 ? newTotalCost / newTotalQuantity : 0,
        totalEssenceUsed: summary.totalEssenceUsed + essenceUsed,
        totalAlcoholUsed: summary.totalAlcoholUsed + alcoholUsed
      };
    }, {
      totalProducts: 0,
      totalQuantity: 0,
      totalCost: 0,
      averageCost: 0,
      averageUnitCost: 0,
      totalEssenceUsed: 0,
      totalAlcoholUsed: 0
    });
  };

  const renderCostItem = ({ item }: { item: Cost }) => (
    <ThemedView style={styles.costItem}>
      <ThemedText type="defaultSemiBold" style={styles.productName}>
        {item.product_name || item.productName || 'Bilinmeyen Ürün'}
      </ThemedText>
      <ThemedView style={styles.costDetails}>
        <ThemedText style={styles.costDetail}>
          Adet: {item.quantity || 0}
        </ThemedText>
        <ThemedText style={styles.costDetail}>
          Birim Maliyet: {(item.unit_cost || item.unitCost || 0).toFixed(2)} ₺
        </ThemedText>
        <ThemedText style={styles.costDetail}>
          Toplam Maliyet: {(item.total_cost || item.totalCost || 0).toFixed(2)} ₺
        </ThemedText>
      </ThemedView>
      <ThemedView style={styles.costBreakdown}>
        <ThemedText style={styles.breakdownItem}>
          Esans: {(item.essence_cost || 0).toFixed(2)} ₺
        </ThemedText>
        <ThemedText style={styles.breakdownItem}>
          Alkol: {(item.alcohol_cost || 0).toFixed(2)} ₺
        </ThemedText>
        <ThemedText style={styles.breakdownItem}>
          Şişe: {(item.bottle_price || item.bottlePrice || 0).toFixed(2)} ₺
        </ThemedText>
      </ThemedView>
    </ThemedView>
  );

  const renderSummary = () => {
    const summary = calculateSummary();
    return (
      <ThemedView style={styles.summaryContainer}>
        <ThemedText type="subtitle" style={styles.summaryTitle}>
          Maliyet Özeti
        </ThemedText>
        <ThemedView style={styles.summaryContent}>
          <ThemedView style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Ürün</ThemedText>
            <ThemedText style={styles.summaryValue}>{summary.totalProducts} adet</ThemedText>
          </ThemedView>
          <ThemedView style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Maliyet</ThemedText>
            <ThemedText style={styles.summaryValue}>{summary.totalCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Ortalama Maliyet</ThemedText>
            <ThemedText style={styles.summaryValue}>{summary.averageCost.toFixed(2)} ₺</ThemedText>
          </ThemedView>
          <ThemedView style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Esans</ThemedText>
            <ThemedText style={styles.summaryValue}>{summary.totalEssenceUsed.toFixed(1)} ml</ThemedText>
          </ThemedView>
          <ThemedView style={styles.summaryItem}>
            <ThemedText style={styles.summaryLabel}>Toplam Alkol</ThemedText>
            <ThemedText style={styles.summaryValue}>{summary.totalAlcoholUsed.toFixed(1)} ml</ThemedText>
          </ThemedView>
        </ThemedView>
      </ThemedView>
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Yükleniyor...</ThemedText>
      </ThemedView>
    );
  }

  if (error) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText style={styles.errorText}>{error}</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      {/* Toggle Buttons */}
      <ThemedView style={styles.toggleContainer}>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            activeTab === 'list' && styles.activeToggleButton,
            { borderColor }
          ]}
          onPress={() => setActiveTab('list')}
        >
          <ThemedText
            style={[
              styles.toggleButtonText,
              activeTab === 'list' && styles.activeToggleButtonText
            ]}
          >
            Maliyet Tablosu
          </ThemedText>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.toggleButton,
            activeTab === 'form' && styles.activeToggleButton,
            { borderColor }
          ]}
          onPress={() => setActiveTab('form')}
        >
          <ThemedText
            style={[
              styles.toggleButtonText,
              activeTab === 'form' && styles.activeToggleButtonText
            ]}
          >
            Maliyet Ekle
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>

      {/* Main Content */}
      {activeTab === 'form' ? (
        <CostForm products={products} onAddCost={handleAddCost} />
      ) : (
        <>
          {renderSummary()}
          <FlatList
            data={costs}
            renderItem={renderCostItem}
            keyExtractor={(item: Cost) => item.id?.toString() || `cost-${Date.now()}-${Math.random()}`}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={
              <ThemedText style={styles.emptyText}>
                Henüz maliyet kaydı bulunmuyor
              </ThemedText>
            }
          />
        </>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  toggleContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  toggleButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeToggleButton: {
    backgroundColor: '#007AFF',
  },
  toggleButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  activeToggleButtonText: {
    color: '#fff',
  },
  listContent: {
    padding: 16,
  },
  costItem: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  productName: {
    fontSize: 18,
    marginBottom: 8,
  },
  costDetails: {
    marginBottom: 8,
  },
  costDetail: {
    fontSize: 16,
    marginBottom: 4,
  },
  costBreakdown: {
    borderTopWidth: 1,
    borderTopColor: '#ddd',
    paddingTop: 8,
  },
  breakdownItem: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  summaryContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    backgroundColor: '#f8f8f8',
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    textAlign: 'center',
  },
  summaryContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  summaryItem: {
    width: '48%',
    marginBottom: 12,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#eee',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  emptyText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginTop: 32,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    margin: 16,
  },
});
